@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: var(--font-barlow), sans-serif;
    overflow-x: hidden; /* Prevent horizontal scroll globally */
  }

  body {
    @apply text-bauhaus-black bg-brand-background;
    font-family: var(--font-barlow), sans-serif;
    overflow-x: hidden; /* Reinforce horizontal scroll prevention */
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
    font-family: var(--font-barlow), sans-serif;
  }

  /* Explicit font-size for h1 to prevent browser warnings */
  h1 {
    font-size: 2rem; /* Default size, can be overridden by utility classes */
  }

  /* Critical CSS for LCP optimization */
  .text-hero {
    font-size: 4rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
    font-weight: 700;
    font-family: var(--font-barlow), sans-serif;
  }

  /* Responsive hero text */
  @media (max-width: 768px) {
    .text-hero {
      font-size: 2.5rem;
    }
  }

  @media (max-width: 640px) {
    .text-hero {
      font-size: 2rem;
    }
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3.5 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-bauhaus-white transition-colors duration-200 rounded-2xl;
  }

  .btn-accent {
    @apply px-6 py-3.5 border-2 font-bold uppercase tracking-wide transition-colors duration-200 rounded-2xl;
  }

  .btn-red {
    @apply btn-accent border-bauhaus-red bg-bauhaus-red text-bauhaus-white hover:bg-transparent hover:text-bauhaus-red;
  }

  .btn-yellow {
    @apply btn-accent border-bauhaus-yellow bg-bauhaus-yellow text-bauhaus-black hover:bg-transparent hover:text-bauhaus-yellow;
  }

  .btn-blue {
    @apply btn-accent border-bauhaus-blue bg-bauhaus-blue text-bauhaus-white hover:bg-transparent hover:text-bauhaus-blue;
  }

  /* Blog Content Styling */
  .blog-content {
    @apply max-w-4xl mx-auto;
  }

  /* Enhanced code blocks */
  .blog-content pre {
    @apply relative;
  }

  .blog-content pre code {
    @apply block p-0 bg-transparent text-inherit;
  }

  /* Custom highlight component styling */
  .blog-content .highlight {
    @apply bg-bauhaus-yellow bg-opacity-20 border-l-4 border-bauhaus-yellow px-6 py-4 my-8 rounded-r-lg;
  }

  .blog-content .highlight p {
    @apply my-0 text-bauhaus-black font-medium;
  }

  /* Table styling enhancements */
  .blog-content table {
    @apply w-full my-8 shadow-sm;
  }

  .blog-content table th {
    @apply bg-bauhaus-blue text-bauhaus-white font-bold;
  }

  .blog-content table tr:nth-child(even) {
    @apply bg-bauhaus-yellow;
  }

  .blog-content table tr:hover {
    @apply bg-bauhaus-red text-bauhaus-white;
  }

  /* Enhanced blockquote styling */
  .blog-content blockquote {
    @apply relative;
  }

  .blog-content blockquote::before {
    content: '"';
    @apply absolute -left-2 -top-2 text-6xl text-bauhaus-blue font-bold leading-none;
  }

  /* Reading experience enhancements */
  .blog-content p + p {
    @apply mt-6;
  }

  .blog-content h2 + p,
  .blog-content h3 + p,
  .blog-content h4 + p {
    @apply mt-4;
  }

  /* Link hover effects */
  .blog-content a {
    @apply transition-all duration-200;
  }

  .blog-content a:hover {
    @apply transform translate-y-[-1px];
  }

  /* List styling */
  .blog-content ul li::marker {
    @apply text-bauhaus-blue;
  }

  .blog-content ol li::marker {
    @apply text-bauhaus-blue font-bold;
  }

  /* Image captions */
  .blog-content img + em {
    @apply block text-center text-sm text-gray-500 mt-2 italic;
  }



  /* Typography enhancements */
  .blog-content {
    line-height: 1.7;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }

  .blog-content p {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Drop cap for first paragraph */
  .blog-content > div > p:first-of-type::first-letter {
    @apply float-left text-6xl font-bold leading-none m-0 -mt-[3px] text-bauhaus-red;
    font-family: var(--font-barlow), serif;
  }

  /* Enhanced focus states for accessibility */
  .blog-content a:focus {
    @apply outline-none ring-2 ring-bauhaus-blue ring-opacity-50 rounded;
  }

  /* Print styles */
  @media print {
    .blog-content {
      @apply text-bauhaus-black;
    }

    .blog-content a {
      @apply text-bauhaus-black no-underline;
    }

    .blog-content a::after {
      content: " (" attr(href) ")";
      @apply text-xs text-bauhaus-black;
    }
  }
}
